"""
Provider Service Management Views for Vierla Beauty Services Marketplace
Django REST Framework views for provider service CRUD operations
"""
from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q

from .models import Service, ServiceProvider
from .serializers import (
    ServiceSerializer, ServiceListSerializer, ServiceCreateUpdateSerializer,
    ProviderServiceCreateSerializer
)
from .filters import ServiceFilter


class IsServiceProvider(permissions.BasePermission):
    """
    Custom permission to only allow service providers to access provider endpoints.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'service_provider' and
            hasattr(request.user, 'provider_profile')
        )


class IsServiceOwner(permissions.BasePermission):
    """
    Custom permission to only allow service owners to edit their services.
    """
    
    def has_object_permission(self, request, view, obj):
        return obj.provider.user == request.user


class ServiceLimitCheck(permissions.BasePermission):
    """
    Custom permission to enforce service limits for unverified providers.
    """
    
    def has_permission(self, request, view):
        if request.method != 'POST':
            return True
        
        try:
            provider = request.user.provider_profile
            if provider.is_verified:
                return True
            
            # Unverified providers limited to 3 active services
            active_services_count = Service.objects.filter(
                provider=provider,
                is_active=True
            ).count()
            
            return active_services_count < 3
        except AttributeError:
            return False


class ProviderServiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for provider service management with full CRUD operations
    """
    
    serializer_class = ServiceSerializer
    permission_classes = [IsServiceProvider, ServiceLimitCheck]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = ServiceFilter
    ordering_fields = ['name', 'base_price', 'duration', 'created_at', 'booking_count']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Return only services belonging to the authenticated provider"""
        if not self.request.user.is_authenticated:
            return Service.objects.none()
        
        try:
            provider = self.request.user.provider_profile
            return Service.objects.filter(
                provider=provider
            ).select_related('category', 'provider').order_by('-created_at')
        except AttributeError:
            return Service.objects.none()
    
    def get_permissions(self):
        """
        Instantiate and return the list of permissions required for this view.
        """
        permissions = [IsServiceProvider()]
        
        if self.action == 'create':
            permissions.append(ServiceLimitCheck())
        elif self.action in ['update', 'partial_update', 'destroy']:
            permissions.append(IsServiceOwner())
        
        return permissions
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceListSerializer
        elif self.action == 'create':
            return ProviderServiceCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return ServiceCreateUpdateSerializer
        return ServiceSerializer
    
    def perform_create(self, serializer):
        """Set the provider when creating a service"""
        provider = self.request.user.provider_profile
        serializer.save(provider=provider)
    
    def perform_destroy(self, instance):
        """Soft delete the service instead of hard delete"""
        instance.is_active = False
        instance.save()
    
    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """Toggle service availability status"""
        service = self.get_object()
        service.is_available = not service.is_available
        service.save()
        
        serializer = self.get_serializer(service)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Bulk update multiple services"""
        service_ids = request.data.get('service_ids', [])
        action_type = request.data.get('action', '')
        
        if not service_ids:
            return Response(
                {'error': 'service_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get services belonging to the provider
        services = self.get_queryset().filter(id__in=service_ids)
        
        if action_type == 'activate':
            services.update(is_available=True)
        elif action_type == 'deactivate':
            services.update(is_available=False)
        elif action_type == 'delete':
            services.update(is_active=False)
        else:
            return Response(
                {'error': 'Invalid action type'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return Response({
            'message': f'Successfully {action_type}d {services.count()} services',
            'updated_count': services.count()
        })
    
    @action(detail=True, methods=['get'])
    def analytics(self, request, pk=None):
        """Get analytics data for a specific service"""
        service = self.get_object()
        
        # TODO: Implement actual analytics calculation
        # For now, return mock data
        analytics_data = {
            'service_id': service.id,
            'total_bookings': service.booking_count,
            'revenue': float(service.base_price) * service.booking_count,
            'average_rating': 4.5,  # Mock data
            'conversion_rate': 0.68,  # Mock data
            'popular_times': ['14:00', '15:00', '16:00'],  # Mock data
            'customer_insights': {
                'new_customers': 15,
                'repeat_customers': 10,
                'total_customers': 25
            }
        }
        
        return Response(analytics_data)
    
    def list(self, request, *args, **kwargs):
        """Override list to add provider-specific metadata"""
        response = super().list(request, *args, **kwargs)
        
        if isinstance(response.data, dict) and 'results' in response.data:
            # Add provider summary data
            try:
                provider = request.user.provider_profile
                total_services = self.get_queryset().count()
                active_services = self.get_queryset().filter(is_available=True).count()

                response.data['provider_summary'] = {
                    'total_services': total_services,
                    'active_services': active_services,
                    'inactive_services': total_services - active_services,
                    'is_verified': provider.is_verified,
                    'service_limit': 3 if not provider.is_verified else None
                }
            except AttributeError:
                # User doesn't have a provider profile
                response.data['provider_summary'] = {
                    'total_services': 0,
                    'active_services': 0,
                    'inactive_services': 0,
                    'is_verified': False,
                    'service_limit': 3
                }
        
        return response

    @action(detail=False, methods=['get'])
    def dashboard_summary(self, request):
        """Get comprehensive dashboard summary for provider"""
        try:
            provider = request.user.provider_profile
            queryset = self.get_queryset()

            # Service statistics
            total_services = queryset.count()
            active_services = queryset.filter(is_available=True).count()
            popular_services = queryset.filter(is_popular=True).count()

            # Revenue calculations (mock for now)
            total_revenue = sum(float(service.base_price) * service.booking_count for service in queryset)

            # Recent services
            recent_services = queryset.order_by('-created_at')[:5]
            recent_services_data = ServiceListSerializer(recent_services, many=True).data

            # Top performing services
            top_services = queryset.order_by('-booking_count')[:5]
            top_services_data = ServiceListSerializer(top_services, many=True).data

            dashboard_data = {
                'provider_info': {
                    'business_name': provider.business_name,
                    'is_verified': provider.is_verified,
                    'rating': float(provider.rating),
                    'total_bookings': provider.total_bookings,
                },
                'service_stats': {
                    'total_services': total_services,
                    'active_services': active_services,
                    'inactive_services': total_services - active_services,
                    'popular_services': popular_services,
                    'service_limit': provider.service_limit,
                    'services_remaining': provider.services_remaining,
                },
                'revenue_stats': {
                    'total_revenue': total_revenue,
                    'average_service_price': total_revenue / total_services if total_services > 0 else 0,
                },
                'recent_services': recent_services_data,
                'top_services': top_services_data,
            }

            return Response(dashboard_data)

        except AttributeError:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def categories_summary(self, request):
        """Get services grouped by category"""
        try:
            queryset = self.get_queryset()
            categories_data = {}

            for service in queryset:
                category_name = service.category.name
                if category_name not in categories_data:
                    categories_data[category_name] = {
                        'category_id': service.category.id,
                        'category_name': category_name,
                        'services_count': 0,
                        'active_services_count': 0,
                        'total_bookings': 0,
                        'services': []
                    }

                categories_data[category_name]['services_count'] += 1
                if service.is_available:
                    categories_data[category_name]['active_services_count'] += 1
                categories_data[category_name]['total_bookings'] += service.booking_count
                categories_data[category_name]['services'].append({
                    'id': service.id,
                    'name': service.name,
                    'base_price': service.base_price,
                    'is_available': service.is_available,
                    'booking_count': service.booking_count
                })

            return Response(list(categories_data.values()))

        except Exception as e:
            return Response(
                {'error': 'Failed to fetch categories summary'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def duplicate(self, request, pk=None):
        """Duplicate an existing service"""
        try:
            original_service = self.get_object()
            provider = request.user.provider_profile

            # Check service limit for unverified providers
            if not provider.is_verified and provider.active_services_count >= 3:
                return Response(
                    {'error': 'Service limit reached. Verify your account to add more services.'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Create duplicate with modified name
            duplicate_name = f"{original_service.name} (Copy)"
            counter = 1
            while Service.objects.filter(provider=provider, name=duplicate_name, is_active=True).exists():
                duplicate_name = f"{original_service.name} (Copy {counter})"
                counter += 1

            # Create the duplicate
            duplicate_service = Service.objects.create(
                provider=provider,
                category=original_service.category,
                name=duplicate_name,
                description=original_service.description,
                short_description=original_service.short_description,
                mobile_description=original_service.mobile_description,
                base_price=original_service.base_price,
                price_type=original_service.price_type,
                max_price=original_service.max_price,
                duration=original_service.duration,
                buffer_time=original_service.buffer_time,
                requirements=original_service.requirements,
                preparation_instructions=original_service.preparation_instructions,
                is_available=False,  # Start as unavailable
                is_popular=False,    # Don't copy popularity
            )

            serializer = self.get_serializer(duplicate_service)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': 'Failed to duplicate service'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
