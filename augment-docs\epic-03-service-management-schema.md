# EPIC-03: Service Management Database Schema Design

## Overview
This document outlines the database schema design for service creation and management functionality for providers in the Vierla application. The schema builds upon the existing models and adds provider-specific management capabilities.

## Current Schema Analysis

### Existing Models (Already Implemented)

#### 1. ServiceCategory Model
**Status**: ✅ Complete - No changes needed
- UUID primary key
- Hierarchical support with parent/child relationships
- Visual elements (icon, color, image)
- Mobile optimization fields
- Status flags (is_popular, is_active)
- Proper indexing for performance

#### 2. ServiceProvider Model  
**Status**: ✅ Complete - No changes needed
- UUID primary key
- One-to-one relationship with User model
- Complete business information (name, description, contact)
- Location data with geolocation support
- Social media integration
- Verification and status management
- Rating and review metrics
- Business metrics tracking

#### 3. Service Model
**Status**: ✅ Complete - Ready for management features
- UUID primary key
- Provider and category relationships
- Flexible pricing models (fixed, hourly, range, consultation)
- Duration and buffer time management
- Service media support
- Status flags (is_active, is_available, is_popular)
- Requirements and preparation instructions
- Booking count tracking
- Proper indexing including provider-specific indexes

## Service Management Schema Requirements

### Provider Permission Model
**Purpose**: Track provider permissions and limitations for service management

```python
class ProviderPermissions(models.Model):
    """
    Provider-specific permissions and limitations for service management
    """
    provider = models.OneToOneField(
        ServiceProvider,
        on_delete=models.CASCADE,
        related_name='permissions'
    )
    
    # Service Limits
    max_services = models.PositiveIntegerField(
        default=3,  # Unverified providers limited to 3
        help_text='Maximum number of active services allowed'
    )
    can_create_services = models.BooleanField(default=True)
    can_edit_services = models.BooleanField(default=True)
    can_delete_services = models.BooleanField(default=True)
    can_bulk_update = models.BooleanField(default=False)  # Premium feature
    
    # Advanced Features
    can_use_pricing_optimization = models.BooleanField(default=False)
    can_access_analytics = models.BooleanField(default=True)
    can_manage_availability = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

### Service Management Audit Log
**Purpose**: Track all service management actions for security and analytics

```python
class ServiceManagementLog(models.Model):
    """
    Audit log for service management actions
    """
    ACTION_CHOICES = [
        ('create', 'Service Created'),
        ('update', 'Service Updated'),
        ('delete', 'Service Deleted'),
        ('activate', 'Service Activated'),
        ('deactivate', 'Service Deactivated'),
        ('bulk_update', 'Bulk Update'),
        ('price_change', 'Price Changed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    provider = models.ForeignKey(ServiceProvider, on_delete=models.CASCADE)
    service = models.ForeignKey(Service, on_delete=models.SET_NULL, null=True)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    
    # Change tracking
    old_values = models.JSONField(default=dict, blank=True)
    new_values = models.JSONField(default=dict, blank=True)
    
    # Metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
```

## Enhanced Service Model Features

### Additional Fields for Management
The existing Service model already supports all required management features:

1. **Provider Ownership**: ✅ `provider` ForeignKey with CASCADE delete
2. **Status Management**: ✅ `is_active`, `is_available` flags
3. **Pricing Flexibility**: ✅ Multiple pricing types with validation
4. **Content Management**: ✅ Multiple description fields for different contexts
5. **Media Support**: ✅ Image field with proper upload path
6. **Metrics Tracking**: ✅ `booking_count` field
7. **Audit Trail**: ✅ `created_at`, `updated_at` timestamps

### Validation Rules for Service Management

```python
# In Service model clean() method
def clean(self):
    # Validate pricing consistency
    if self.price_type == 'range' and self.max_price:
        if self.max_price <= self.base_price:
            raise ValidationError("Maximum price must be greater than base price")
    
    # Validate duration
    if self.duration <= 0:
        raise ValidationError("Duration must be greater than 0 minutes")
    
    # Validate provider service limits
    if not self.pk:  # New service
        provider_permissions = getattr(self.provider, 'permissions', None)
        if provider_permissions:
            active_services = self.provider.services.filter(is_active=True).count()
            if active_services >= provider_permissions.max_services:
                raise ValidationError(
                    f"Provider has reached maximum service limit of {provider_permissions.max_services}"
                )
```

## Database Indexes for Performance

### Service Management Specific Indexes
```python
# Additional indexes for service management queries
class Meta:
    indexes = [
        # Existing indexes...
        models.Index(fields=['provider', 'is_active', 'created_at']),  # Provider dashboard
        models.Index(fields=['provider', 'category', 'is_active']),   # Category filtering
        models.Index(fields=['provider', 'base_price']),              # Price sorting
        models.Index(fields=['provider', 'booking_count']),           # Performance metrics
    ]
```

## API Endpoint Schema

### Service Management Endpoints
1. **GET /api/provider/services/** - List provider services
2. **POST /api/provider/services/** - Create new service
3. **PUT /api/provider/services/{id}/** - Update service
4. **DELETE /api/provider/services/{id}/** - Delete service
5. **PATCH /api/provider/services/{id}/toggle-active/** - Toggle active status
6. **POST /api/provider/services/bulk-update/** - Bulk update services

### Permission Requirements
- All endpoints require `IsAuthenticated` and `IsProviderUser` permissions
- Service ownership validation for individual service operations
- Provider permission checks for advanced features

## Migration Strategy

### Phase 1: Permission Model (Optional Enhancement)
- Add ProviderPermissions model if advanced permission control is needed
- Create default permissions for existing providers

### Phase 2: Audit Logging (Optional Enhancement)  
- Add ServiceManagementLog model for audit trail
- Implement logging in service management views

### Phase 3: Enhanced Validation
- Add custom validation methods to Service model
- Implement provider service limit checks

## Conclusion

The current database schema is already well-designed and supports all required service management functionality. The existing Service, ServiceProvider, and ServiceCategory models provide:

- ✅ Complete CRUD operations support
- ✅ Proper relationships and constraints
- ✅ Flexible pricing and content management
- ✅ Status and availability tracking
- ✅ Performance-optimized indexing
- ✅ Mobile-first design considerations

**No schema changes are required** for basic service management functionality. The optional enhancements (ProviderPermissions and ServiceManagementLog) can be added later if advanced features are needed.
