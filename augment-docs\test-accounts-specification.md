# Test Accounts Specification for Vierla Application

## Overview
This document specifies the comprehensive test account system for the Vierla application, based on the reference implementation and adapted to our new conventions and standards.

## Test Account Categories

### 1. Customer Test Accounts

#### Primary Customer Account
```
Email: <EMAIL>
Password: VierlaTest123!
Role: customer
First Name: Emma
Last Name: Thompson
Profile: Marketing Manager, High budget, Regular user
Phone: +****************
City: Ottawa
State: Ontario
Country: Canada
Account Status: active
Email Verified: true
```

#### Additional Customer Accounts
```
Email: <EMAIL>
Password: VierlaTest123!
Role: customer
First Name: Sarah
Last Name: Johnson
Profile: Premium customer, frequent bookings
Phone: +****************
City: Toronto
State: Ontario

Email: <EMAIL>
Password: VierlaTest123!
Role: customer
First Name: Michael
Last Name: Chen
Profile: Regular customer, occasional bookings
Phone: +****************
City: Vancouver
State: British Columbia

Email: <EMAIL>
Password: VierlaTest123!
Role: customer
First Name: Priya
Last Name: Patel
Profile: New customer, first-time user
Phone: +****************
City: Calgary
State: Alberta
```

### 2. Service Provider Test Accounts

#### Hair Services Providers
```
Email: <EMAIL>
Password: VierlaTest123!
Role: service_provider
First Name: Emma
Last Name: Rodriguez
Business Name: Trendy Cuts Salon
Category: Hair Services
City: Ottawa
State: Ontario
Phone: +****************
Business Email: <EMAIL>
Address: 123 Rideau Street
Zip Code: K1N 5X8
Is Verified: true
Rating: 4.8
Years Experience: 8
```

```
Email: <EMAIL>
Password: VierlaTest123!
Role: service_provider
First Name: Marcus
Last Name: Johnson
Business Name: Elite Cuts Barbershop
Category: Hair Services
City: Toronto
State: Ontario
Phone: +****************
Business Email: <EMAIL>
Address: 456 Queen Street West
Zip Code: M5V 2A8
Is Verified: true
Rating: 4.9
Years Experience: 12
```

#### Nail Services Providers
```
Email: <EMAIL>
Password: VierlaTest123!
Role: service_provider
First Name: Lisa
Last Name: Wang
Business Name: Luxe Nail Lounge
Category: Nail Services
City: Ottawa
State: Ontario
Phone: +****************
Business Email: <EMAIL>
Address: 789 Bank Street
Zip Code: K1S 3T4
Is Verified: true
Rating: 4.7
Years Experience: 6
```

#### Lash Services Providers
```
Email: <EMAIL>
Password: VierlaTest123!
Role: service_provider
First Name: Sophia
Last Name: Martinez
Business Name: Lash Studio Elite
Category: Lash Services
City: Vancouver
State: British Columbia
Phone: +****************
Business Email: <EMAIL>
Address: 321 Robson Street
Zip Code: V6B 3C2
Is Verified: true
Rating: 4.9
Years Experience: 5
```

#### Massage Therapy Providers
```
Email: <EMAIL>
Password: VierlaTest123!
Role: service_provider
First Name: David
Last Name: Thompson
Business Name: Wellness Massage Therapy
Category: Massage
City: Calgary
State: Alberta
Phone: +****************
Business Email: <EMAIL>
Address: 654 17th Avenue SW
Zip Code: T2S 0B5
Is Verified: true
Rating: 4.8
Years Experience: 10
```

#### Unverified Provider (for testing limits)
```
Email: <EMAIL>
Password: VierlaTest123!
Role: service_provider
First Name: Alex
Last Name: Rivera
Business Name: New Beauty Studio
Category: Hair Services
City: Montreal
State: Quebec
Phone: +****************
Business Email: <EMAIL>
Address: 987 Saint-Catherine Street
Zip Code: H3A 1E1
Is Verified: false
Rating: 4.2
Years Experience: 2
```

### 3. Administrative Accounts

#### Super Admin Account
```
Email: <EMAIL>
Password: VierlaAdmin123!
Role: admin
First Name: System
Last Name: Administrator
Is Staff: true
Is Superuser: true
Account Status: active
```

#### Support Staff Account
```
Email: <EMAIL>
Password: VierlaSupport123!
Role: admin
First Name: Customer
Last Name: Support
Is Staff: true
Is Superuser: false
Account Status: active
```

## Test Services Data

### Hair Services
- Haircut & Style ($75, 60 min)
- Hair Color & Highlights ($150, 120 min)
- Hair Wash & Blowdry ($45, 45 min)
- Beard Trim ($35, 30 min)
- Hair Treatment ($85, 90 min)

### Nail Services
- Classic Manicure ($40, 45 min)
- Gel Manicure ($55, 60 min)
- Classic Pedicure ($50, 60 min)
- Nail Art ($25, 30 min)
- Acrylic Nails ($70, 90 min)

### Lash Services
- Classic Lash Extensions ($120, 120 min)
- Volume Lash Extensions ($150, 150 min)
- Lash Lift & Tint ($80, 75 min)
- Lash Removal ($40, 45 min)
- Brow Shaping ($35, 30 min)

### Massage Services
- Swedish Massage ($90, 60 min)
- Deep Tissue Massage ($110, 60 min)
- Hot Stone Massage ($130, 90 min)
- Couples Massage ($200, 60 min)
- Chair Massage ($60, 30 min)

## Implementation Requirements

### Database Seeding
1. **Management Command**: `python manage.py create_test_accounts`
2. **Seed Data Command**: `python manage.py seed_test_data`
3. **Full Reset Command**: `python manage.py reset_test_data --force`

### Security Considerations
- All test accounts must be clearly marked with `is_test_account=True`
- Test accounts should be automatically disabled in production
- Test passwords follow strong password requirements
- Test emails use `.vierla.app` domain to avoid conflicts

### Development Environment Setup
- Test accounts should be created automatically during development setup
- Seed data should include realistic booking history
- Test accounts should have proper profile images and business photos
- Geographic distribution across major Canadian cities

### API Access Information
```
Base URL: http://localhost:8000/api/
Admin Panel: http://localhost:8000/admin/
API Documentation: http://localhost:8000/api/docs/
```

## Usage Guidelines

### For Development
- Use customer accounts to test booking flows
- Use provider accounts to test service management
- Use admin accounts for administrative testing
- Test both verified and unverified provider scenarios

### For Testing
- Automated tests should use factory-generated accounts
- Integration tests should use these predefined accounts
- Performance tests should use bulk-generated accounts
- Security tests should include privilege escalation scenarios

### For Demonstrations
- Use the primary accounts for client demonstrations
- Ensure all accounts have complete, realistic profiles
- Maintain consistent data quality across all test accounts
- Regular cleanup and refresh of test data

## Maintenance

### Regular Updates
- Monthly password rotation for security
- Quarterly profile data refresh
- Annual review of account requirements
- Continuous monitoring of account usage

### Data Quality
- Ensure all accounts have complete profiles
- Maintain realistic booking and service history
- Keep geographic and demographic diversity
- Regular validation of account functionality

This specification ensures comprehensive test coverage while maintaining data quality and security standards for the Vierla application development and testing processes.
